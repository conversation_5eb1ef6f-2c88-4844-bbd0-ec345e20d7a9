{"appId": "com.ccseva.app", "productName": "CC<PERSON>eva", "directories": {"output": "release"}, "files": ["dist/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.developer-tools", "icon": "assets/icon.icns", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "hardenedRuntime": true, "gatekeeperAssess": false, "extendInfo": {"LSUIElement": true, "CFBundleDisplayName": "CC<PERSON>eva", "CFBundleName": "CC<PERSON>eva", "CFBundleExecutable": "CC<PERSON>eva", "NSHumanReadableCopyright": "Copyright © 2024 CCSeva. All rights reserved.", "CFBundleDocumentTypes": [], "LSMinimumSystemVersion": "10.15.0"}}, "dmg": {"title": "CC<PERSON>eva", "icon": "assets/icon.icns"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}