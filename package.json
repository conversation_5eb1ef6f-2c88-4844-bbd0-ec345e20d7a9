{"name": "ccseva", "version": "1.2.1", "description": "CCSeva - A beautiful macOS menu bar app for real-time Claude Code usage tracking", "main": "dist/main.js", "scripts": {"start": "electron .", "dev": "webpack --mode development --watch --progress && electron .", "build": "webpack --mode production && tsc --project tsconfig.main.json && tsc --project tsconfig.preload.json", "electron-dev": "concurrently \"npm run dev\" \"wait-on dist/bundle.js && electron .\"", "pack": "electron-builder", "dist": "npm run build && electron-builder", "dist:mac": "npm run build && electron-builder --mac", "lint": "biome lint ./src", "lint:fix": "biome lint --write ./src", "format": "biome format --write ./src", "format:check": "biome format ./src", "check": "biome check ./src", "check:fix": "biome check --write ./src", "type-check": "tsc --noEmit", "prepare": "husky"}, "keywords": ["electron", "ccseva", "claude-code", "usage-monitor", "macos", "menu-bar", "ai", "tokens"], "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/Iamshankhadeep/ccseva.git"}, "homepage": "https://github.com/Iamshankhadeep/ccseva#readme", "bugs": {"url": "https://github.com/Iamshankhadeep/ccseva/issues"}, "type": "module", "devDependencies": {"@biomejs/biome": "^1.9.4", "@types/node": "^24.0.3", "concurrently": "^9.1.2", "css-loader": "^7.1.2", "electron": "^36.5.0", "electron-builder": "^26.0.12", "html-webpack-plugin": "^5.6.3", "husky": "^9.1.7", "postcss-loader": "^8.1.1", "style-loader": "^4.0.0", "tailwindcss": "^3.4.17", "ts-loader": "^9.5.2", "typescript": "^5.8.3", "wait-on": "^8.0.3", "webpack": "^5.99.9", "webpack-cli": "^6.0.1"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "ccusage": "15.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "lucide-react": "^0.523.0", "next-themes": "^0.4.6", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1"}}