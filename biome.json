{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "organizeImports": {"enabled": true}, "files": {"include": ["src/**/*.ts", "src/**/*.tsx", "*.ts", "*.js"], "ignore": ["node_modules/**", "dist/**", "release/**", "build/**", "*.d.ts"]}, "linter": {"enabled": true, "rules": {"recommended": true, "suspicious": {"noExplicitAny": "warn", "noArrayIndexKey": "warn"}, "style": {"noNonNullAssertion": "warn", "useImportType": "error"}, "correctness": {"useExhaustiveDependencies": "warn"}, "security": {"noDangerouslySetInnerHtml": "error"}, "complexity": {"noExcessiveCognitiveComplexity": "warn"}, "a11y": {"noSvgWithoutTitle": "off"}}}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineWidth": 100, "lineEnding": "lf", "attributePosition": "auto"}, "javascript": {"formatter": {"quoteStyle": "single", "jsxQuoteStyle": "double", "trailingCommas": "es5", "semicolons": "always", "arrowParentheses": "always", "bracketSpacing": true, "bracketSameLine": false}}, "json": {"formatter": {"enabled": true}, "linter": {"enabled": true}}}