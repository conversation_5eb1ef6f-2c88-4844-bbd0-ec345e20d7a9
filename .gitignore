# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/
release/
*.dmg
*.exe
*.AppImage

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Temporary folders
tmp/
temp/

# Electron specific
electron-builder-effective-config.yaml

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Webpack
.webpack/

# Electron-Builder output
/dist_electron/

# Local Netlify folder
.netlify

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Yarn Integrity file
.yarn-integrity

# Biome
.biome-cache/

# GitHub Actions
.github/workflows/secrets/

# Security
.env.example
secrets/
private/

# Testing
coverage/
test-results/
playwright-report/
test-output/

# Editor files
*.sublime-project
*.sublime-workspace
.vscode/settings.json
.vscode/launch.json
.vscode/extensions.json
.history/

# macOS specific
.AppleDouble
.LSOverride
Icon?

# Archives
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Logs
*.log.*
logs/
pids/
lib-cov/

# Runtime
.cache/
.tmp/
.temp/

# Build artifacts
*.tsbuildinfo
build-info.json
stats.json

# Package managers
package-lock.json.bak
yarn.lock.bak
.pnpm-store/
.yarn/cache/
.yarn/unplugged/
.yarn/build-state.yml
.yarn/install-state.gz
