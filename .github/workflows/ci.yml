name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: macos-latest
    
    strategy:
      matrix:
        node-version: [18.x, 20.x]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run Biome linting
      run: npm run lint
    
    - name: Run Biome formatting check
      run: npm run format:check
    
    - name: Type check
      run: npm run type-check
    
    - name: Build application
      run: npm run build
    
    - name: Run tests
      run: npm test
      continue-on-error: true  # Tests might not exist yet
    
    - name: Package application (macOS only)
      if: matrix.node-version == '20.x'
      run: npm run pack
      env:
        GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  security:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Use Node.js 20.x
      uses: actions/setup-node@v4
      with:
        node-version: 20.x
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run security audit
      run: npm audit --audit-level=high
      continue-on-error: true
    
    - name: Check for vulnerable dependencies
      run: npm audit --omit=dev --audit-level=moderate