name: Feature Request
description: Suggest a new feature or enhancement for Claude Monitor
labels: [enhancement]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to suggest a feature! Please fill out the form below with as much detail as possible.

  - type: checkboxes
    id: prerequisites
    attributes:
      label: Prerequisites
      description: Please confirm the following before submitting a feature request
      options:
        - label: I have searched existing issues to make sure this feature hasn't been requested
          required: true
        - label: I have read the project roadmap and this feature isn't already planned
          required: true

  - type: textarea
    id: problem
    attributes:
      label: Problem Statement
      description: Is your feature request related to a problem? Please describe.
      placeholder: A clear and concise description of what the problem is. Ex. I'm always frustrated when [...]
    validations:
      required: true

  - type: textarea
    id: solution
    attributes:
      label: Proposed Solution
      description: Describe the solution you'd like
      placeholder: A clear and concise description of what you want to happen
    validations:
      required: true

  - type: textarea
    id: alternatives
    attributes:
      label: Alternatives Considered
      description: Describe any alternative solutions or features you've considered
      placeholder: A clear and concise description of any alternative solutions or features you've considered

  - type: dropdown
    id: priority
    attributes:
      label: Priority
      description: How important is this feature to you?
      options:
        - Low - Nice to have
        - Medium - Would be helpful
        - High - Important for my workflow
        - Critical - Blocking my usage
    validations:
      required: true

  - type: dropdown
    id: category
    attributes:
      label: Feature Category
      description: What category does this feature fall into?
      options:
        - UI/UX - User interface improvements
        - Analytics - Data analysis and reporting
        - Notifications - Alert and notification system
        - Performance - Speed and efficiency improvements
        - Integration - Third-party service integration
        - Customization - User preferences and settings
        - Other - Please specify in additional context
    validations:
      required: true

  - type: textarea
    id: use-case
    attributes:
      label: Use Case
      description: Describe your specific use case for this feature
      placeholder: How would you use this feature? What workflow would it improve?
    validations:
      required: true

  - type: textarea
    id: mockups
    attributes:
      label: Mockups or Examples
      description: If applicable, add mockups, screenshots, or examples to help explain your feature
      placeholder: Drag and drop images here or paste them, or provide links to examples

  - type: textarea
    id: implementation
    attributes:
      label: Implementation Ideas
      description: If you have ideas about how this could be implemented, please share them
      placeholder: Any technical details or implementation suggestions

  - type: textarea
    id: additional
    attributes:
      label: Additional Context
      description: Add any other context or information about the feature request here
      placeholder: Any other information that might be helpful...