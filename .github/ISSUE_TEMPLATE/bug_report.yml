name: Bug Report
description: Report a bug or issue with Claude Monitor
labels: [bug]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to report a bug! Please fill out the form below with as much detail as possible.

  - type: checkboxes
    id: prerequisites
    attributes:
      label: Prerequisites
      description: Please confirm the following before submitting a bug report
      options:
        - label: I have searched existing issues to make sure this bug hasn't been reported
          required: true
        - label: I have read the troubleshooting section in the README
          required: true
        - label: I am using the latest version of Claude Monitor
          required: true

  - type: textarea
    id: description
    attributes:
      label: Bug Description
      description: A clear and concise description of what the bug is
      placeholder: Describe the issue you're experiencing...
    validations:
      required: true

  - type: textarea
    id: reproduction
    attributes:
      label: Steps to Reproduce
      description: Steps to reproduce the behavior
      placeholder: |
        1. Go to '...'
        2. Click on '...'
        3. Scroll down to '...'
        4. See error
    validations:
      required: true

  - type: textarea
    id: expected
    attributes:
      label: Expected Behavior
      description: A clear and concise description of what you expected to happen
      placeholder: What should have happened instead?
    validations:
      required: true

  - type: textarea
    id: actual
    attributes:
      label: Actual Behavior
      description: A clear and concise description of what actually happened
      placeholder: What actually happened?
    validations:
      required: true

  - type: textarea
    id: screenshots
    attributes:
      label: Screenshots
      description: If applicable, add screenshots to help explain your problem
      placeholder: Drag and drop images here or paste them

  - type: dropdown
    id: os
    attributes:
      label: Operating System
      description: What operating system are you using?
      options:
        - macOS 14 (Sonoma)
        - macOS 13 (Ventura) 
        - macOS 12 (Monterey)
        - macOS 11 (Big Sur)
        - macOS 10.15 (Catalina)
        - Other (please specify in additional context)
    validations:
      required: true

  - type: input
    id: version
    attributes:
      label: CCSeva Version
      description: What version of CCSeva are you using?
      placeholder: e.g. 1.0.0
    validations:
      required: true

  - type: input
    id: node-version
    attributes:
      label: Node.js Version
      description: What version of Node.js are you using? (if building from source)
      placeholder: e.g. 18.17.0

  - type: input
    id: claude-version
    attributes:
      label: Claude Code Version
      description: What version of Claude Code CLI are you using?
      placeholder: e.g. 1.2.3

  - type: textarea
    id: logs
    attributes:
      label: Error Logs
      description: If applicable, please include any error messages or logs
      placeholder: Paste error messages or logs here
      render: shell

  - type: textarea
    id: additional
    attributes:
      label: Additional Context
      description: Add any other context about the problem here
      placeholder: Any other information that might be helpful...