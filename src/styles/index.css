@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Fira Code Font */
@import url('https://fonts.googleapis.com/css2?family=Fira+Code:wght@300;400;500;600;700&display=swap');

/* Custom Properties for Claude Code Design System */
:root {
  /* Claude Code Color Swatches */
  --swatch--slate-dark: #1c1917;
  --swatch--slate-medium: #44403c;
  --swatch--slate-light: #78716c;
  --swatch--cloud-light: #fafaf9;
  --swatch--cloud-medium: #f5f5f4;
  --swatch--cloud-dark: #e7e5e4;

  /* Warm Neutral Palette */
  --color-neutral-50: #fafaf9;
  --color-neutral-100: #f5f5f4;
  --color-neutral-200: #e7e5e4;
  --color-neutral-300: #d6d3d1;
  --color-neutral-400: #a8a29e;
  --color-neutral-500: #78716c;
  --color-neutral-600: #57534e;
  --color-neutral-700: #44403c;
  --color-neutral-800: #292524;
  --color-neutral-900: #1c1917;

  /* <PERSON> Code Accent Colors */
  --color-primary: rgba(204, 120, 92, 1);
  --color-primary-light: rgba(204, 120, 92, 0.8);
  --color-primary-dark: rgba(184, 100, 72, 1);
  --color-accent: #ff6b35;
  --color-accent-light: #ff8c5c;

  /* Status Colors with Warm Undertones */
  --color-success: #15803d;
  --color-success-light: #22c55e;
  --color-warning: #ea580c;
  --color-warning-light: #fb7c2d;
  --color-error: #dc2626;
  --color-error-light: #ef4444;

  /* Claude Code Gradients */
  --gradient-primary: linear-gradient(135deg, rgba(204, 120, 92, 1) 0%, rgba(184, 100, 72, 1) 100%);
  --gradient-secondary: linear-gradient(135deg, #ff6b35 0%, #ea580c 100%);
  --gradient-success: linear-gradient(135deg, #15803d 0%, #166534 100%);
  --gradient-warning: linear-gradient(135deg, #ea580c 0%, #dc2626 100%);
  --gradient-error: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  --gradient-glass: linear-gradient(135deg, rgba(250, 250, 249, 0.08) 0%, rgba(250, 250, 249, 0.04) 100%);
  --gradient-backdrop: linear-gradient(135deg, #1c1917 0%, #292524 50%, #1c1917 100%);

  /* Subtle Shadows for Clean Aesthetic */
  --shadow-xs: 0 1px 2px rgba(28, 25, 23, 0.04);
  --shadow-sm: 0 1px 3px rgba(28, 25, 23, 0.08), 0 1px 2px rgba(28, 25, 23, 0.06);
  --shadow-md: 0 4px 6px rgba(28, 25, 23, 0.06), 0 2px 4px rgba(28, 25, 23, 0.04);
  --shadow-lg: 0 10px 15px rgba(28, 25, 23, 0.08), 0 4px 6px rgba(28, 25, 23, 0.04);
  --shadow-xl: 0 20px 25px rgba(28, 25, 23, 0.08), 0 10px 10px rgba(28, 25, 23, 0.03);
  --shadow-2xl: 0 25px 50px rgba(28, 25, 23, 0.15);
  --shadow-glass: 0 8px 32px rgba(68, 64, 60, 0.15);
  --shadow-glow: 0 0 20px rgba(204, 120, 92, 0.2);

  /* Border Radius */
  --radius-xs: 4px;
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 20px;
  --radius-3xl: 24px;
  --radius-full: 9999px;

  /* Transitions */
  --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* Spacing */
  --spacing-unit: 4px;

  /* Fira Code Typography System */
  --font-family-primary: 'Fira Code', ui-monospace, SFMono-Regular, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
  --font-family-mono: 'Fira Code', ui-monospace, SFMono-Regular, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* Responsive Typography */
  --font-size-xs: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);
  --font-size-sm: clamp(0.875rem, 0.825rem + 0.25vw, 1rem);
  --font-size-base: clamp(1rem, 0.95rem + 0.25vw, 1.125rem);
  --font-size-lg: clamp(1.125rem, 1.075rem + 0.25vw, 1.25rem);
  --font-size-xl: clamp(1.25rem, 1.15rem + 0.5vw, 1.5rem);
  --font-size-2xl: clamp(1.5rem, 1.35rem + 0.75vw, 1.875rem);

  /* Refined Glass Morphism */
  --glass-backdrop: rgba(250, 250, 249, 0.06);
  --glass-border: rgba(250, 250, 249, 0.08);
  --glass-shadow: 0 8px 32px rgba(68, 64, 60, 0.15);
  --glass-blur: blur(12px);
  
  /* Fluid Spacing */
  --spacing-fluid-xs: clamp(0.25rem, 0.2rem + 0.25vw, 0.5rem);
  --spacing-fluid-sm: clamp(0.5rem, 0.4rem + 0.5vw, 1rem);
  --spacing-fluid-md: clamp(1rem, 0.8rem + 1vw, 2rem);
  --spacing-fluid-lg: clamp(1.5rem, 1.2rem + 1.5vw, 3rem);
  --spacing-fluid-xl: clamp(2rem, 1.6rem + 2vw, 4rem);
}

/* Base Styles */
* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.6;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-normal);
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--color-neutral-100);
  background: var(--gradient-backdrop);
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

#root {
  height: 100%;
  width: 100%;
  overflow-x: hidden;
  overflow-y: auto;
}

/* Refined Scrollbar */
::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

::-webkit-scrollbar-track {
  background: rgba(250, 250, 249, 0.03);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
  transition: var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary-light);
  box-shadow: var(--shadow-glow);
}

/* Animated Background */
.app-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-backdrop);
  z-index: -2;
}

.app-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(204, 120, 92, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 107, 53, 0.06) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 113, 108, 0.04) 0%, transparent 50%);
  animation: backgroundShift 20s ease-in-out infinite;
}

/* Gradient background utility */
.gradient-bg {
  background: var(--gradient-backdrop);
  position: relative;
  min-height: 100vh;
}

.gradient-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(204, 120, 92, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 107, 53, 0.06) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 113, 108, 0.04) 0%, transparent 50%);
  animation: backgroundShift 20s ease-in-out infinite;
  z-index: -1;
}

@keyframes backgroundShift {

  0%,
  100% {
    transform: translateX(0) translateY(0) rotate(0deg);
    opacity: 0.8;
  }

  33% {
    transform: translateX(20px) translateY(-20px) rotate(2deg);
    opacity: 1;
  }

  66% {
    transform: translateX(-20px) translateY(20px) rotate(-2deg);
    opacity: 0.9;
  }
}

/* Glass Morphism Components */
.glass {
  background: var(--glass-backdrop);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  box-shadow: var(--glass-shadow);
  position: relative;
  overflow: hidden;
}

.glass::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(250, 250, 249, 0.2), transparent);
  animation: shimmer 3s infinite;
}

.glass-card {
  @apply glass;
  transition: var(--transition-normal);
}

.glass-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl), var(--shadow-glow);
}

.glass-interactive {
  cursor: pointer;
}

.glass-interactive:active {
  transform: translateY(0) scale(0.98);
}

/* Claude Code Button Styles */
.btn {
  @apply inline-flex items-center justify-center;
  @apply px-4 py-2 rounded-lg font-medium;
  @apply transition-all duration-200 ease-in-out;
  @apply border border-transparent;
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
  @apply relative overflow-hidden;

  font-family: var(--font-family-primary);
  font-size: var(--font-size-sm);
  background: rgba(250, 250, 249, 0.06);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(250, 250, 249, 0.1);
  color: var(--color-neutral-100);
  text-shadow: 0 1px 2px rgba(28, 25, 23, 0.4);

  transition: var(--transition-normal);
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(250, 250, 249, 0.1), transparent);
  transition: var(--transition-normal);
}

.btn:hover::before {
  left: 100%;
}

.btn:hover {
  transform: translateY(-1px) scale(1.02);
  box-shadow: var(--shadow-lg);
  border-color: rgba(250, 250, 249, 0.15);
  background: rgba(250, 250, 249, 0.08);
}

.btn:active {
  transform: translateY(0) scale(0.98);
}

.btn-primary {
  background: var(--gradient-primary);
  border-color: var(--color-primary-light);
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  box-shadow: var(--shadow-lg), var(--shadow-glow);
}

.btn-ghost {
  background: transparent;
  border-color: rgba(250, 250, 249, 0.08);
}

.btn-ghost:hover {
  background: rgba(250, 250, 249, 0.06);
  border-color: rgba(250, 250, 249, 0.12);
}

/* Claude Code Progress Bar */
.progress-container {
  @apply w-full bg-neutral-warm-800/40 rounded-full overflow-hidden;
  position: relative;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(250, 250, 249, 0.08);
}

.progress-bar {
  @apply h-full rounded-full transition-all duration-1000 ease-out;
  position: relative;
  background: var(--gradient-primary);
  box-shadow: 0 0 20px rgba(204, 120, 92, 0.3);
}

.progress-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(250, 250, 249, 0.2), transparent);
  animation: progressShine 2s infinite;
}

@keyframes progressShine {
  0% {
    transform: translateX(-100%);
  }

  50% {
    transform: translateX(100%);
  }

  100% {
    transform: translateX(100%);
  }
}


/* Loading Animations */
.loading-spinner {
  @apply w-8 h-8 border-4 border-white/20 border-t-white rounded-full;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* Loading Dots */
.loading-dots {
  @apply flex space-x-1;
}

.loading-dot {
  @apply w-2 h-2 bg-white/60 rounded-full;
  animation: bounce 1.4s infinite ease-in-out both;
}

.loading-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dot:nth-child(2) {
  animation-delay: -0.16s;
}

.loading-dot:nth-child(3) {
  animation-delay: 0s;
}

@keyframes bounce {

  0%,
  80%,
  100% {
    transform: scale(0);
  }

  40% {
    transform: scale(1);
  }
}

/* Utility Classes */
.hover-lift {
  transition: var(--transition-normal);
}

.hover-lift:hover {
  transform: translateY(-2px);
}

.hover-scale {
  transition: var(--transition-normal);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-glow {
  transition: var(--transition-normal);
}

.hover-glow:hover {
  box-shadow: var(--shadow-glow);
}

.text-gradient {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-shadow {
  text-shadow: 0 2px 4px rgba(28, 25, 23, 0.4);
}

.floating {
  animation: float 3s cubic-bezier(0.4, 0, 0.2, 1) infinite;
}

@keyframes float {

  0%,
  100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-10px);
  }
}


.stagger-children>* {
  opacity: 0;
  animation: staggerIn 0.6s ease-out forwards;
}

.stagger-children>*:nth-child(1) {
  animation-delay: 0.1s;
}

.stagger-children>*:nth-child(2) {
  animation-delay: 0.2s;
}

.stagger-children>*:nth-child(3) {
  animation-delay: 0.3s;
}

.stagger-children>*:nth-child(4) {
  animation-delay: 0.4s;
}

.stagger-children>*:nth-child(5) {
  animation-delay: 0.5s;
}

.stagger-children>*:nth-child(6) {
  animation-delay: 0.6s;
}

@keyframes staggerIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

/* Claude Code Notifications */
.notification {
  @apply glass-card;
  padding: var(--spacing-fluid-sm);
  margin-bottom: var(--spacing-fluid-xs);
  border-left: 3px solid var(--color-primary);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-sm);
  transform: translateX(100%);
  transition: var(--transition-normal);
}

.notification.show {
  transform: translateX(0);
}

.notification.success {
  border-left-color: var(--color-success);
  box-shadow: var(--shadow-lg), 0 0 20px rgba(21, 128, 61, 0.15);
}

.notification.warning {
  border-left-color: var(--color-warning);
  box-shadow: var(--shadow-lg), 0 0 20px rgba(234, 88, 12, 0.15);
}

.notification.error {
  border-left-color: var(--color-error);
  box-shadow: var(--shadow-lg), 0 0 20px rgba(220, 38, 38, 0.15);
}

.notification.info {
  border-left-color: var(--color-primary);
  box-shadow: var(--shadow-lg), 0 0 20px rgba(204, 120, 92, 0.15);
}

/* Responsive Design */
@media (max-width: 768px) {
  .glass-card {
    margin: 0.5rem;
  }

  .btn {
    @apply px-3 py-2 text-sm;
  }
}


::selection {
  background: rgba(204, 120, 92, 0.25);
  color: var(--color-neutral-100);
}

::-moz-selection {
  background: rgba(204, 120, 92, 0.25);
  color: var(--color-neutral-100);
}

/* Claude Code Navigation */
.nav-item {
  @apply rounded-lg transition-all duration-200;
  font-family: var(--font-family-primary);
  font-size: var(--font-size-sm);
  background: transparent;
  border: 1px solid transparent;
}

.nav-item:hover {
  background: rgba(250, 250, 249, 0.06);
  border-color: rgba(250, 250, 249, 0.1);
  transform: translateX(1px) scale(1.02);
}

.nav-item.active {
  background: var(--gradient-primary);
  border-color: var(--color-primary-light);
  box-shadow: var(--shadow-md), 0 0 20px rgba(204, 120, 92, 0.2);
}

.nav-item.active:hover {
  transform: translateX(0) scale(1.02);
}


/* Enhanced Progress Ring */
.progress-ring-circle {
  transition: stroke-dasharray 1s ease-in-out;
}

/* Z-Index Layer Utilities */
.z-base {
  z-index: 1;
}

.z-dropdown {
  z-index: 999;
}

.z-overlay {
  z-index: 998;
}

.z-modal {
  z-index: 1000;
}

.z-tooltip {
  z-index: 9999;
}

/* Enhanced Overlay Styles */
.overlay-backdrop {
  background: rgba(28, 25, 23, 0.8);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.dropdown-content {
  background: rgba(41, 37, 36, 0.95);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid rgba(250, 250, 249, 0.1);
  box-shadow: 0 10px 25px rgba(28, 25, 23, 0.3);
}

.tooltip-content {
  background: rgba(41, 37, 36, 0.95);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid rgba(250, 250, 249, 0.15);
  box-shadow: 0 8px 20px rgba(28, 25, 23, 0.4);
}


